import { Link, useLocation } from 'react-router-dom';
import { Calendar, Plus, Home, School } from 'lucide-react';

const Layout = ({ children }) => {
  const location = useLocation();

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-blue-600 text-white shadow-lg">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <School className="h-8 w-8" />
              <div>
                <h1 className="text-2xl font-bold">Colegio Nueva Vida</h1>
                <p className="text-blue-100 text-sm">Sistema de Eventos</p>
              </div>
            </div>
            
            <nav className="hidden md:flex space-x-6">
              <Link 
                to="/" 
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                  isActive('/') ? 'bg-blue-700' : 'hover:bg-blue-500'
                }`}
              >
                <Home className="h-4 w-4" />
                <span>Eventos</span>
              </Link>
              <Link 
                to="/add-event" 
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                  isActive('/add-event') ? 'bg-blue-700' : 'hover:bg-blue-500'
                }`}
              >
                <Plus className="h-4 w-4" />
                <span>Agregar Evento</span>
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Mobile Navigation */}
      <nav className="md:hidden bg-blue-500 text-white">
        <div className="container mx-auto px-4 py-2">
          <div className="flex space-x-4">
            <Link 
              to="/" 
              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm ${
                isActive('/') ? 'bg-blue-700' : 'hover:bg-blue-400'
              }`}
            >
              <Home className="h-4 w-4" />
              <span>Eventos</span>
            </Link>
            <Link 
              to="/add-event" 
              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm ${
                isActive('/add-event') ? 'bg-blue-700' : 'hover:bg-blue-400'
              }`}
            >
              <Plus className="h-4 w-4" />
              <span>Agregar</span>
            </Link>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-12">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <School className="h-6 w-6" />
            <span className="text-lg font-semibold">Colegio Nueva Vida</span>
          </div>
          <p className="text-gray-400">
            Sistema de Gestión de Eventos Escolares
          </p>
          <p className="text-gray-500 text-sm mt-2">
            © 2024 Colegio Nueva Vida. Todos los derechos reservados.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
